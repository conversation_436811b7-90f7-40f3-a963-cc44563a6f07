from rxn4chemistry import RXN4ChemistryWrapper
import time
import requests

# API credentials
API_KEY = "apk-56d66d912338dc5344a7fbdc39339d5c119d5d655529f5964978d5b90609d1c5"
PROJECT_ID = "6870ee4095b316f61f21aac6"

print("Checking RXN API rate limits and status...")

# Initialize wrapper
rxn = RXN4ChemistryWrapper(api_key=API_KEY, project_id=PROJECT_ID)

# Try to make a simple request and see what headers we get back
print("\nTesting with a simple API call to check rate limit headers...")

try:
    # Let's try to access the underlying session to see headers
    # First, let's see what methods are available
    print("Available methods on rxn object:")
    methods = [method for method in dir(rxn) if not method.startswith('_')]
    for method in methods[:10]:  # Show first 10 methods
        print(f"  - {method}")
    
    # Try to get project info which should be a lighter call
    print("\nTrying to get project information...")
    projects = rxn.list_all_projects()
    print(f"Projects response type: {type(projects)}")
    print(f"Projects content: {projects}")
    
except Exception as e:
    print(f"Error: {e}")

# Let's wait a very long time and try again
print(f"\nWaiting 10 minutes before trying a prediction...")
print("This will help us understand if it's a time-based rate limit...")

for i in range(10):
    print(f"Waiting... {i+1}/10 minutes")
    time.sleep(60)

print("Now trying prediction after 10 minute wait...")
try:
    reaction_input = "BrBr.c1ccc2cc3ccccc3cc2c1"
    response = rxn.predict_reaction(reaction_input)
    print("Response after long wait:", response)
except Exception as e:
    print(f"Error after long wait: {e}")
