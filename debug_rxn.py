#!/usr/bin/env python3
"""
Debug script to check IBM RXN API response structure
"""

from rxn4chemistry import RXN4ChemistryWrapper
import json

# Configuration
API_KEY = "apk-56d66d912338dc5344a7fbdc39339d5c119d5d655529f5964978d5b90609d1c5"
PROJECT_ID = "6870ee4095b316f61f21aac6"

def debug_rxn_response():
    """Debug the RXN API response structure."""
    try:
        rxn = RXN4ChemistryWrapper(api_key=API_KEY, project_id=PROJECT_ID)
        print("✓ Successfully initialized RXN4Chemistry client")
        
        # Try a simple prediction
        reactants = "BrBr.c1ccc2cc3ccccc3cc2c1"
        print(f"🧪 Testing prediction for: {reactants}")
        
        response = rxn.predict_reaction(reactants)
        print("\n📋 Full response structure:")
        print(json.dumps(response, indent=2))
        
        # Check what keys are available
        print(f"\n🔑 Available keys in response: {list(response.keys())}")
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_rxn_response()
