#!/usr/bin/env python3
"""
Example usage of IBM RXN prediction script

This demonstrates how to use the IBM RXN prediction functionality
with some common organic chemistry reactions.
"""

from ibm_rxn_prediction import initialize_rxn_client, predict_reaction_product, display_results

def run_examples():
    """Run some example predictions."""
    
    # Initialize the RXN client
    rxn = initialize_rxn_client()
    if not rxn:
        print("Failed to initialize RXN client")
        return
    
    # Define some interesting reactions to test
    reactions = {
        "Bromination of Naphthalene": "BrBr.c1ccc2cc3ccccc3cc2c1",
        "Esterification": "CCO.CC(=O)O",
        "Chlorination of Benzene": "c1ccccc1.Cl2",
        "Grignard Formation": "BrCCCC.Mg",
        "Aldol Condensation": "CC(=O)C.CC=O",
    }
    
    print("Running Example Reactions")
    print("=" * 50)
    
    for name, smiles in reactions.items():
        print(f"\n🧪 {name}")
        print(f"Reactants: {smiles}")
        print("-" * 30)
        
        result = predict_reaction_product(rxn, smiles)
        display_results(result)
        
        print("\n" + "="*50)

if __name__ == "__main__":
    run_examples()
