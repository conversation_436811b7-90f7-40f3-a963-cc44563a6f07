#!/usr/bin/env python3
"""
Simple IBM RXN Product Prediction Script

This script uses IBM RXN for Chemistry to predict reaction products
given starting materials in SMILES format.

Usage:
    python ibm_rxn_prediction.py
"""

import time
from rxn4chemistry import RXN4ChemistryWrapper

# Configuration
API_KEY = "apk-56d66d912338dc5344a7fbdc39339d5c119d5d655529f5964978d5b90609d1c5"
PROJECT_ID = "6870ee4095b316f61f21aac6"

def initialize_rxn_client():
    """Initialize the RXN4Chemistry wrapper with API key and project ID."""
    try:
        rxn = RXN4ChemistryWrapper(api_key=API_KEY, project_id=PROJECT_ID)
        print("✓ Successfully initialized RXN4Chemistry client")
        return rxn
    except Exception as e:
        print(f"✗ Failed to initialize RXN client: {e}")
        return None

def predict_reaction_product(rxn, reactants_smiles):
    """
    Predict the product of a chemical reaction given starting materials.
    
    Args:
        rxn: RXN4Chemistry wrapper instance
        reactants_smiles (str): Starting materials in SMILES format, separated by '.'
        
    Returns:
        dict: Prediction results or None if failed
    """
    try:
        print(f"🧪 Predicting reaction for: {reactants_smiles}")
        
        # Submit prediction request
        response = rxn.predict_reaction(reactants_smiles)
        prediction_id = response['prediction_id']
        print(f"📋 Prediction ID: {prediction_id}")
        
        # Poll for results
        print("⏳ Waiting for prediction results...")
        max_attempts = 30  # Maximum number of polling attempts
        attempt = 0
        
        while attempt < max_attempts:
            result = rxn.get_predict_reaction_results(prediction_id)
            status = result["response"]["payload"]["status"]
            
            print(f"   Status: {status} (attempt {attempt + 1}/{max_attempts})")
            
            if status == "SUCCESS":
                print("✓ Prediction completed successfully!")
                return result
            elif status == "FAILURE":
                print("✗ Prediction failed")
                return None
            
            # Wait before next poll
            time.sleep(2)
            attempt += 1
        
        print("⚠️  Prediction timed out")
        return None
        
    except Exception as e:
        print(f"✗ Error during prediction: {e}")
        return None

def display_results(result):
    """Display the prediction results in a readable format."""
    if not result:
        print("No results to display")
        return
    
    try:
        attempts = result['response']['payload']['attempts']
        
        print("\n" + "="*50)
        print("PREDICTION RESULTS")
        print("="*50)
        
        for i, attempt in enumerate(attempts, 1):
            smiles = attempt['smiles']
            confidence = attempt.get('confidence', 'N/A')
            
            print(f"\nPrediction {i}:")
            print(f"  Reaction SMILES: {smiles}")
            print(f"  Confidence: {confidence}")
            
            # Split reaction SMILES to show reactants and products
            if '>>' in smiles:
                reactants, products = smiles.split('>>')
                print(f"  Reactants: {reactants}")
                print(f"  Products: {products}")
        
    except KeyError as e:
        print(f"✗ Error parsing results: {e}")
        print("Raw result:", result)

def main():
    """Main function to run the prediction."""
    print("IBM RXN Product Prediction Script")
    print("=" * 40)
    
    # Initialize RXN client
    rxn = initialize_rxn_client()
    if not rxn:
        return
    
    # Example reactions - you can modify these or make them interactive
    example_reactions = [
        "BrBr.c1ccc2cc3ccccc3cc2c1",  # Bromination of naphthalene
        "CCO.CC(=O)O",  # Ethanol + Acetic acid
        "c1ccccc1.Cl2"  # Benzene + Chlorine
    ]
    
    print("\nRunning example predictions:")
    print("-" * 30)
    
    for i, reactants in enumerate(example_reactions, 1):
        print(f"\nExample {i}: {reactants}")
        result = predict_reaction_product(rxn, reactants)
        display_results(result)
        
        if i < len(example_reactions):
            print("\n" + "-"*30)
    
    # Interactive mode
    print("\n" + "="*50)
    print("INTERACTIVE MODE")
    print("="*50)
    print("Enter your own reactants in SMILES format (separated by '.')")
    print("Type 'quit' to exit")
    
    while True:
        try:
            user_input = input("\nEnter reactants SMILES: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if not user_input:
                print("Please enter valid SMILES")
                continue
            
            result = predict_reaction_product(rxn, user_input)
            display_results(result)
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
