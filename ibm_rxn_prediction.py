#!/usr/bin/env python3
"""
Simple IBM RXN Product Prediction Script

This script uses IBM RXN for Chemistry to predict reaction products
given starting materials in SMILES format.

Usage:
    python ibm_rxn_prediction.py
"""

import time
from rxn4chemistry import RXN4ChemistryWrapper

# Configuration
API_KEY = "apk-56d66d912338dc5344a7fbdc39339d5c119d5d655529f5964978d5b90609d1c5"
PROJECT_ID = "6870ee4095b316f61f21aac6"

def initialize_rxn_client():
    """Initialize the RXN4Chemistry wrapper with API key and project ID."""
    try:
        rxn = RXN4ChemistryWrapper(api_key=API_KEY, project_id=PROJECT_ID)
        print("✓ Successfully initialized RXN4Chemistry client")
        return rxn
    except Exception as e:
        print(f"✗ Failed to initialize RXN client: {e}")
        return None

def predict_reaction_product(rxn, reactants_smiles):
    """
    Predict the product of a chemical reaction given starting materials.

    Args:
        rxn: RXN4Chemistry wrapper instance
        reactants_smiles (str): Starting materials in SMILES format, separated by '.'

    Returns:
        dict: Prediction results or None if failed
    """
    try:
        print(f"🧪 Predicting reaction for: {reactants_smiles}")

        # IBM RXN has strict rate limiting: 1 request per 15-30 seconds
        # Wait 30 seconds before making any request to be safe
        print("⏳ Waiting 30 seconds to respect IBM RXN rate limits...")
        time.sleep(30)

        prediction_id = None

        try:
            print("📤 Submitting prediction request...")
            response = rxn.predict_reaction(reactants_smiles)

            # Check if we got a rate limit error
            if 'response' in response and 'status' in response['response']:
                if response['response']['status'] == 429:
                    print("⚠️  Rate limited! You may need to wait longer between requests.")
                    print("   IBM RXN allows only 1 request per 15-30 seconds.")
                    print("   Please wait at least 60 seconds before trying again.")
                    return None
                elif response['response']['status'] != 200:
                    print(f"✗ API Error: {response['response'].get('message', 'Unknown error')}")
                    return None

            # Check for prediction_id in different possible locations
            if 'prediction_id' in response:
                prediction_id = response['prediction_id']
            elif 'response' in response and 'payload' in response['response'] and 'id' in response['response']['payload']:
                prediction_id = response['response']['payload']['id']
            elif 'response' in response and 'payload' in response['response'] and 'prediction_id' in response['response']['payload']:
                prediction_id = response['response']['payload']['prediction_id']

            if not prediction_id:
                print(f"✗ No prediction ID found in response.")
                print("Response structure:")
                import json
                print(json.dumps(response, indent=2))
                return None

            print(f"📋 Prediction ID: {prediction_id}")

        except Exception as e:
            print(f"✗ Request failed: {e}")
            return None

        # Poll for results
        print("⏳ Waiting for prediction results...")
        max_attempts = 30  # Maximum number of polling attempts
        attempt = 0

        while attempt < max_attempts:
            try:
                result = rxn.get_predict_reaction_results(prediction_id)

                # Handle different response structures
                status = None
                if 'response' in result and 'payload' in result['response'] and 'status' in result['response']['payload']:
                    status = result["response"]["payload"]["status"]
                elif 'status' in result:
                    status = result['status']

                if not status:
                    print(f"⚠️  Could not determine status from result: {result}")
                    time.sleep(3)
                    attempt += 1
                    continue

                print(f"   Status: {status} (attempt {attempt + 1}/{max_attempts})")

                if status == "SUCCESS":
                    print("✓ Prediction completed successfully!")
                    return result
                elif status == "FAILURE":
                    print("✗ Prediction failed")
                    return None

                # Wait before next poll with exponential backoff
                wait_time = min(2 * (1.5 ** attempt), 10)  # Cap at 10 seconds
                time.sleep(wait_time)
                attempt += 1

            except Exception as e:
                print(f"⚠️  Error polling results: {e}")
                time.sleep(3)
                attempt += 1

        print("⚠️  Prediction timed out")
        return None

    except Exception as e:
        print(f"✗ Error during prediction: {e}")
        import traceback
        traceback.print_exc()
        return None

def display_results(result):
    """Display the prediction results in a readable format."""
    if not result:
        print("No results to display")
        return

    try:
        # Try to find attempts in different possible locations
        attempts = None

        if 'response' in result and 'payload' in result['response'] and 'attempts' in result['response']['payload']:
            attempts = result['response']['payload']['attempts']
        elif 'attempts' in result:
            attempts = result['attempts']
        elif 'response' in result and 'payload' in result['response'] and 'sequences' in result['response']['payload']:
            # Sometimes results are in 'sequences' instead of 'attempts'
            attempts = result['response']['payload']['sequences']

        if not attempts:
            print("✗ No prediction attempts found in result")
            print("Available keys in result:", list(result.keys()) if isinstance(result, dict) else "Not a dict")
            if isinstance(result, dict) and 'response' in result:
                print("Available keys in response:", list(result['response'].keys()))
                if 'payload' in result['response']:
                    print("Available keys in payload:", list(result['response']['payload'].keys()))
            return

        print("\n" + "="*50)
        print("PREDICTION RESULTS")
        print("="*50)

        for i, attempt in enumerate(attempts, 1):
            smiles = attempt.get('smiles', 'N/A')
            confidence = attempt.get('confidence', 'N/A')

            print(f"\nPrediction {i}:")
            print(f"  Reaction SMILES: {smiles}")
            print(f"  Confidence: {confidence}")

            # Split reaction SMILES to show reactants and products
            if '>>' in smiles:
                reactants, products = smiles.split('>>')
                print(f"  Reactants: {reactants}")
                print(f"  Products: {products}")

    except Exception as e:
        print(f"✗ Error parsing results: {e}")
        print("Raw result structure:")
        import json
        try:
            print(json.dumps(result, indent=2))
        except:
            print(result)

def main():
    """Main function to run the prediction."""
    print("IBM RXN Product Prediction Script")
    print("=" * 40)
    
    # Initialize RXN client
    rxn = initialize_rxn_client()
    if not rxn:
        return
    
    # Due to IBM RXN's strict rate limiting (1 request per 15-30 seconds),
    # we'll only run one example at a time
    example_reaction = "CCO.CC(=O)O"  # Simple esterification: Ethanol + Acetic acid

    print(f"\nRunning single example prediction:")
    print("-" * 40)
    print(f"Reaction: {example_reaction}")
    print("Note: IBM RXN has strict rate limits (1 request per 15-30 seconds)")

    result = predict_reaction_product(rxn, example_reaction)
    display_results(result)
    
    # Interactive mode
    print("\n" + "="*50)
    print("INTERACTIVE MODE")
    print("="*50)
    print("Enter your own reactants in SMILES format (separated by '.')")
    print("⚠️  WARNING: Each prediction requires a 30+ second wait due to IBM RXN rate limits")
    print("Type 'quit' to exit")

    while True:
        try:
            user_input = input("\nEnter reactants SMILES: ").strip()

            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break

            if not user_input:
                print("Please enter valid SMILES")
                continue

            print(f"⏳ This will take at least 30 seconds due to rate limiting...")
            result = predict_reaction_product(rxn, user_input)
            display_results(result)

        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
