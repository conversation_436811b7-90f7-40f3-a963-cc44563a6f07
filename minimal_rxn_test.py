from rxn4chemistry import RXN4ChemistryWrapper
import time

# API credentials
API_KEY = "apk-56d66d912338dc5344a7fbdc39339d5c119d5d655529f5964978d5b90609d1c5"
PROJECT_ID = "6870ee4095b316f61f21aac6"

print("=== Minimal RXN Test ===")
print("This test will wait 30 minutes before making any API calls")
print("to ensure we don't hit rate limits.")

# Wait 30 minutes
print("Waiting 30 minutes to avoid rate limiting...")
for i in range(30):
    print(f"Minute {i+1}/30 - waiting...")
    time.sleep(60)

print("\nInitializing wrapper after wait...")
rxn = RXN4ChemistryWrapper(api_key=API_KEY, project_id=PROJECT_ID)

print("Making single prediction request...")
try:
    # Use exact format from documentation
    reaction_input = "BrBr.c1ccc2cc3ccccc3cc2c1"
    print(f"Input: {reaction_input}")
    
    response = rxn.predict_reaction(reaction_input)
    print(f"Response: {response}")
    
    if 'prediction_id' in response:
        print(f"✅ Success! Prediction ID: {response['prediction_id']}")
    else:
        print("❌ No prediction_id in response")
        
except Exception as e:
    print(f"❌ Error: {e}")

print("Test completed.")
