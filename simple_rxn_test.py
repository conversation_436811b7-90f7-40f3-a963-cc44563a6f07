#!/usr/bin/env python3
"""
Simple test script for IBM RXN with aggressive rate limiting handling
"""

import time
import json
from rxn4chemistry import RXN4ChemistryWrapper

# Configuration
API_KEY = "apk-56d66d912338dc5344a7fbdc39339d5c119d5d655529f5964978d5b90609d1c5"
PROJECT_ID = "6870ee4095b316f61f21aac6"

def test_single_prediction():
    """Test a single prediction with very conservative rate limiting."""
    try:
        print("Initializing RXN client...")
        rxn = RXN4ChemistryWrapper(api_key=API_KEY, project_id=PROJECT_ID)
        print("✓ Client initialized successfully")
        
        # Wait a bit before making any requests
        print("⏳ Waiting 10 seconds before making request...")
        time.sleep(10)
        
        reactants = "CCO.CC(=O)O"  # Simple esterification
        print(f"🧪 Testing prediction for: {reactants}")
        
        # Try prediction with very long delays
        max_retries = 5
        base_delay = 30  # Start with 30 seconds
        
        for attempt in range(max_retries):
            try:
                print(f"Attempt {attempt + 1}/{max_retries}")
                response = rxn.predict_reaction(reactants)
                
                print("Raw response:")
                print(json.dumps(response, indent=2))
                
                # Check response status
                if 'response' in response:
                    if 'status' in response['response']:
                        status = response['response']['status']
                        if status == 429:
                            delay = base_delay * (2 ** attempt)  # Exponential backoff
                            print(f"⚠️  Rate limited (429). Waiting {delay} seconds...")
                            time.sleep(delay)
                            continue
                        elif status != 200:
                            print(f"✗ API Error {status}: {response['response'].get('message', 'Unknown')}")
                            return
                
                # Look for prediction ID
                prediction_id = None
                if 'prediction_id' in response:
                    prediction_id = response['prediction_id']
                elif 'response' in response and 'payload' in response['response']:
                    payload = response['response']['payload']
                    if 'id' in payload:
                        prediction_id = payload['id']
                    elif 'prediction_id' in payload:
                        prediction_id = payload['prediction_id']
                
                if prediction_id:
                    print(f"✓ Got prediction ID: {prediction_id}")
                    
                    # Poll for results
                    print("⏳ Polling for results...")
                    for poll_attempt in range(20):
                        time.sleep(5)  # Wait 5 seconds between polls
                        
                        try:
                            result = rxn.get_predict_reaction_results(prediction_id)
                            print(f"Poll {poll_attempt + 1}: {json.dumps(result, indent=2)}")
                            
                            # Check status
                            if 'response' in result and 'payload' in result['response']:
                                status = result['response']['payload'].get('status', 'UNKNOWN')
                                print(f"Status: {status}")
                                
                                if status == 'SUCCESS':
                                    print("✓ Prediction successful!")
                                    return result
                                elif status == 'FAILURE':
                                    print("✗ Prediction failed")
                                    return None
                            
                        except Exception as e:
                            print(f"Error polling: {e}")
                            time.sleep(5)
                    
                    print("⚠️  Polling timed out")
                    return None
                else:
                    print("✗ No prediction ID found in response")
                    return None
                    
            except Exception as e:
                print(f"Error on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt)
                    print(f"Waiting {delay} seconds before retry...")
                    time.sleep(delay)
        
        print("✗ All attempts failed")
        return None
        
    except Exception as e:
        print(f"✗ Fatal error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_prediction()
