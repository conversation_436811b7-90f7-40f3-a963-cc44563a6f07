#!/usr/bin/env python3
"""
Test IBM RXN batch prediction API which might have different rate limits
"""

import time
import json
from rxn4chemistry import RXN4ChemistryWrapper

# Configuration
API_KEY = "apk-56d66d912338dc5344a7fbdc39339d5c119d5d655529f5964978d5b90609d1c5"
PROJECT_ID = "6870ee4095b316f61f21aac6"

def test_batch_prediction():
    """Test batch prediction which might have different rate limits."""
    
    print("IBM RXN Batch Prediction Test")
    print("=" * 40)
    
    try:
        # Initialize client
        print("Initializing RXN client...")
        rxn = RXN4ChemistryWrapper(api_key=API_KEY, project_id=PROJECT_ID)
        print("✓ Client initialized successfully")
        
        # Wait to avoid rate limiting
        print("⏳ Waiting 90 seconds to avoid rate limiting...")
        for i in range(9):
            print(f"   ... {(9-i)*10} seconds remaining")
            time.sleep(10)
        
        # Try batch prediction
        reactants_list = ['CCO.CC(=O)O']  # Just one reaction for now
        print(f"\nTesting batch prediction for: {reactants_list}")
        
        try:
            response = rxn.predict_reaction_batch(precursors_list=reactants_list)
            print("✓ Batch request completed without exception")
            
            print("\nBatch response structure:")
            print(json.dumps(response, indent=4))
            
            # Look for task_id
            task_id = None
            if 'task_id' in response:
                task_id = response['task_id']
                print(f"✓ Found task_id: {task_id}")
            elif 'response' in response and isinstance(response['response'], dict):
                if 'payload' in response['response'] and isinstance(response['response']['payload'], dict):
                    payload = response['response']['payload']
                    if 'task_id' in payload:
                        task_id = payload['task_id']
                        print(f"✓ Found task_id in payload: {task_id}")
            
            if task_id:
                print(f"\n⏳ Polling for batch results with task_id: {task_id}")
                
                for attempt in range(20):
                    time.sleep(5)
                    try:
                        result = rxn.get_predict_reaction_batch_results(task_id)
                        print(f"Poll {attempt + 1}: Status check...")
                        
                        if isinstance(result, dict) and 'predictions' in result:
                            print("✓ Batch prediction completed!")
                            print("\nBatch results:")
                            print(json.dumps(result, indent=4))
                            return result
                        else:
                            print(f"   Still processing... (result: {result})")
                            
                    except Exception as e:
                        print(f"   Error polling: {e}")
                
                print("⚠️  Batch polling timed out")
            else:
                print("✗ No task_id found in batch response")
                
        except Exception as e:
            print(f"✗ Batch request failed: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"✗ Failed to initialize client: {e}")

if __name__ == "__main__":
    test_batch_prediction()
