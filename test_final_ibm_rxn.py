#!/usr/bin/env python3
"""
Final test for IBM RXN with project ID and enhanced rate limiting
"""

import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ibm_rxn_with_project():
    """Test IBM RXN with the provided project ID."""
    print("🚀 Testing IBM RXN with Project ID")
    print("=" * 60)
    
    try:
        from product_predictor import ProductPredictor
        
        # Initialize predictor
        print("🔧 Initializing ProductPredictor with project ID...")
        predictor = ProductPredictor()
        
        # Check configuration
        print(f"\n📊 Configuration Status:")
        print(f"   IBM RXN API Key: {'✅' if predictor.ibm_rxn_api_key else '❌'}")
        print(f"   IBM RXN Project ID: {'✅' if predictor.ibm_rxn_project_id else '❌'}")
        print(f"   IBM RXN Wrapper: {'✅' if predictor.rxn_wrapper else '❌'}")
        
        if predictor.ibm_rxn_project_id:
            print(f"   Project ID: {predictor.ibm_rxn_project_id}")
        
        if not predictor.rxn_wrapper:
            print("❌ IBM RXN wrapper not initialized")
            return False
        
        # Test case: Simple esterification reaction
        print(f"\n🧪 Testing Reaction: Ethanol + Acetic Acid → Ethyl Acetate")
        reactant_a = "CCO"  # Ethanol
        reactant_b = "CC(=O)O"  # Acetic acid
        expected = "CC(=O)OCC"  # Ethyl acetate
        
        print(f"   Reactants: {reactant_a} + {reactant_b}")
        print(f"   Expected: {expected}")
        
        # Test IBM RXN directly
        try:
            print(f"\n📡 Testing IBM RXN prediction...")
            products = predictor._predict_with_rxn(reactant_a, reactant_b, 298.15, "standard")
            
            if products:
                print(f"✅ IBM RXN Success!")
                print(f"   Products found: {len(products)}")
                for i, product in enumerate(products, 1):
                    print(f"   Product {i}: {product}")
                
                # Check if we got the expected product or similar
                if expected in products:
                    print(f"🎯 Exact match found!")
                elif any('CC(=O)O' in p for p in products):
                    print(f"🎯 Related ester product found!")
                else:
                    print(f"⚠️  Different products predicted")
                
                return True
            else:
                print(f"❌ IBM RXN returned no products")
                return False
                
        except Exception as e:
            print(f"❌ IBM RXN test failed: {e}")
            import traceback
            print(f"   Details: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"❌ Failed to initialize: {e}")
        return False

def test_complete_pipeline():
    """Test the complete prediction pipeline."""
    print(f"\n🔬 Testing Complete Prediction Pipeline")
    print("=" * 60)
    
    try:
        from product_predictor import ProductPredictor
        predictor = ProductPredictor()
        
        # Test with the complete pipeline
        print(f"🧪 Testing complete pipeline with IBM RXN priority...")
        
        results = predictor.predict_and_validate(
            "CCO",  # Ethanol
            "CC(=O)O",  # Acetic acid
            temperature=298.15,
            use_ai=True,
            ai_method="auto"
        )
        
        if results and 'predicted_products' in results:
            products = results['predicted_products']
            source = results.get('prediction_source', 'Unknown')
            
            print(f"\n📊 Pipeline Results:")
            print(f"   Prediction Source: {source}")
            print(f"   Products Found: {len(products) if products else 0}")
            
            if products:
                print(f"   Products:")
                for i, product in enumerate(products, 1):
                    print(f"      {i}. {product}")
                
                # Check if IBM RXN was used
                if source == 'IBM_RXN':
                    print(f"🎉 SUCCESS: IBM RXN was used as primary predictor!")
                    return True
                else:
                    print(f"⚠️  IBM RXN not used, fell back to: {source}")
                    return False
            else:
                print(f"❌ No products predicted")
                return False
        else:
            print(f"❌ Invalid pipeline results")
            return False
            
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        return False

def test_rate_limiting():
    """Test rate limiting behavior."""
    print(f"\n⏱️ Testing Rate Limiting Behavior")
    print("=" * 40)
    
    try:
        from product_predictor import ProductPredictor
        predictor = ProductPredictor()
        
        if not predictor.rxn_wrapper:
            print("❌ IBM RXN not available for rate limiting test")
            return False
        
        print("🔄 Testing multiple rapid requests...")
        
        test_reactions = [
            ("CCO", "CC(=O)O"),  # Ethanol + Acetic acid
            ("CO", "C=O"),       # Methanol + Formaldehyde
        ]
        
        success_count = 0
        
        for i, (reactant_a, reactant_b) in enumerate(test_reactions, 1):
            print(f"\n   Request {i}: {reactant_a} + {reactant_b}")
            
            try:
                products = predictor._predict_with_rxn(reactant_a, reactant_b, 298.15, "standard")
                if products:
                    print(f"   ✅ Success: {len(products)} products")
                    success_count += 1
                else:
                    print(f"   ⚠️  No products returned")
            except Exception as e:
                print(f"   ❌ Failed: {e}")
        
        print(f"\n📊 Rate Limiting Test Results:")
        print(f"   Successful requests: {success_count}/{len(test_reactions)}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ Rate limiting test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Final IBM RXN Test Suite")
    print("=" * 80)
    
    # Test IBM RXN with project ID
    rxn_success = test_ibm_rxn_with_project()
    
    # Test complete pipeline
    pipeline_success = test_complete_pipeline()
    
    # Test rate limiting
    rate_limit_success = test_rate_limiting()
    
    # Final summary
    print(f"\n{'='*80}")
    print(f"📈 FINAL TEST RESULTS")
    print(f"{'='*80}")
    print(f"   IBM RXN Direct Test: {'✅ PASS' if rxn_success else '❌ FAIL'}")
    print(f"   Complete Pipeline: {'✅ PASS' if pipeline_success else '❌ FAIL'}")
    print(f"   Rate Limiting: {'✅ PASS' if rate_limit_success else '❌ FAIL'}")
    
    total_success = sum([rxn_success, pipeline_success, rate_limit_success])
    
    if total_success == 3:
        print(f"\n🎉 ALL TESTS PASSED! IBM RXN is working perfectly!")
        print(f"   ✅ Project ID configured correctly")
        print(f"   ✅ Rate limiting implemented")
        print(f"   ✅ Priority system working (IBM RXN → OpenAI → Rules)")
    elif total_success >= 1:
        print(f"\n⚠️  PARTIAL SUCCESS ({total_success}/3 tests passed)")
        print(f"   System is functional but may need fine-tuning")
    else:
        print(f"\n❌ ALL TESTS FAILED")
        print(f"   Check API key, project ID, and network connectivity")
    
    print(f"{'='*80}")
