#!/usr/bin/env python3
"""
Diagnostic script for IBM RXN API to check credentials and response structure
"""

import time
import json
from rxn4chemistry import RXN4ChemistryWrapper

# Configuration
API_KEY = "apk-56d66d912338dc5344a7fbdc39339d5c119d5d655529f5964978d5b90609d1c5"
PROJECT_ID = "6870ee4095b316f61f21aac6"

def diagnose_rxn_api():
    """Diagnose IBM RXN API connectivity and response structure."""
    
    print("IBM RXN API Diagnostic Tool")
    print("=" * 40)
    
    try:
        # Initialize client
        print("1. Initializing RXN client...")
        rxn = RXN4ChemistryWrapper(api_key=API_KEY, project_id=PROJECT_ID)
        print("   ✓ Client initialized successfully")
        
        # Check if we can access any basic info
        print("\n2. Testing basic API connectivity...")
        
        # Wait to ensure no rate limiting from previous requests
        print("   ⏳ Waiting 60 seconds to ensure clean slate for rate limiting...")
        for i in range(6):
            print(f"   ... {(6-i)*10} seconds remaining")
            time.sleep(10)
        
        # Try a simple prediction
        reactants = "CCO.CC(=O)O"  # Simple esterification
        print(f"\n3. Testing prediction request for: {reactants}")
        
        try:
            response = rxn.predict_reaction(reactants)
            print("   ✓ Request completed without exception")
            
            print("\n4. Response analysis:")
            print("   Full response structure:")
            print(json.dumps(response, indent=4))
            
            # Analyze response
            if isinstance(response, dict):
                print(f"\n   Top-level keys: {list(response.keys())}")
                
                if 'response' in response:
                    resp = response['response']
                    print(f"   'response' keys: {list(resp.keys()) if isinstance(resp, dict) else 'Not a dict'}")
                    
                    if isinstance(resp, dict) and 'status' in resp:
                        status = resp['status']
                        print(f"   Status code: {status}")
                        
                        if status == 200:
                            print("   ✓ Success! API is working")
                            if 'payload' in resp:
                                print(f"   Payload keys: {list(resp['payload'].keys())}")
                        elif status == 401:
                            print("   ✗ Authentication failed - check API key")
                        elif status == 403:
                            print("   ✗ Forbidden - check project ID or permissions")
                        elif status == 429:
                            print("   ⚠️  Rate limited - need to wait longer")
                        else:
                            print(f"   ⚠️  Unexpected status: {status}")
                            if 'message' in resp:
                                print(f"   Message: {resp['message']}")
                
                # Look for prediction ID
                prediction_id = None
                if 'prediction_id' in response:
                    prediction_id = response['prediction_id']
                    print(f"   ✓ Found prediction_id: {prediction_id}")
                elif 'response' in response and isinstance(response['response'], dict):
                    if 'payload' in response['response'] and isinstance(response['response']['payload'], dict):
                        payload = response['response']['payload']
                        if 'id' in payload:
                            prediction_id = payload['id']
                            print(f"   ✓ Found prediction ID in payload.id: {prediction_id}")
                        elif 'prediction_id' in payload:
                            prediction_id = payload['prediction_id']
                            print(f"   ✓ Found prediction ID in payload.prediction_id: {prediction_id}")
                
                if not prediction_id:
                    print("   ⚠️  No prediction ID found in response")
            else:
                print(f"   ⚠️  Response is not a dict: {type(response)}")
                
        except Exception as e:
            print(f"   ✗ Request failed: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"✗ Failed to initialize client: {e}")
        import traceback
        traceback.print_exc()

def check_credentials():
    """Check if the credentials look valid."""
    print("\nCredential Check:")
    print("-" * 20)
    print(f"API Key: {API_KEY[:20]}...{API_KEY[-10:]} (length: {len(API_KEY)})")
    print(f"Project ID: {PROJECT_ID} (length: {len(PROJECT_ID)})")
    
    # Basic validation
    if not API_KEY.startswith('apk-'):
        print("⚠️  API key doesn't start with 'apk-'")
    else:
        print("✓ API key format looks correct")
    
    if len(PROJECT_ID) != 24:
        print("⚠️  Project ID length is not 24 characters (typical MongoDB ObjectId length)")
    else:
        print("✓ Project ID length looks correct")

if __name__ == "__main__":
    check_credentials()
    diagnose_rxn_api()
