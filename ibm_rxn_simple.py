#!/usr/bin/env python3
"""
Simple IBM RXN Product Prediction Script with Manual Rate Limiting

This script provides a simple interface for IBM RXN predictions with 
clear instructions for handling rate limits.

Usage:
    python ibm_rxn_simple.py
"""

import time
import json
from rxn4chemistry import RXN4ChemistryWrapper

# Configuration
API_KEY = "apk-56d66d912338dc5344a7fbdc39339d5c119d5d655529f5964978d5b90609d1c5"
PROJECT_ID = "6870ee4095b316f61f21aac6"

class IBMRXNPredictor:
    def __init__(self):
        self.rxn = None
        self.last_request_time = 0
        
    def initialize(self):
        """Initialize the RXN client."""
        try:
            self.rxn = RXN4ChemistryWrapper(api_key=API_KEY, project_id=PROJECT_ID)
            print("✓ Successfully initialized IBM RXN client")
            return True
        except Exception as e:
            print(f"✗ Failed to initialize RXN client: {e}")
            return False
    
    def predict_reaction(self, reactants_smiles, wait_time=0):
        """
        Predict reaction products with manual rate limiting control.
        
        Args:
            reactants_smiles (str): Reactants in SMILES format
            wait_time (int): Seconds to wait before making request (0 = no wait)
        """
        if not self.rxn:
            print("✗ RXN client not initialized")
            return None
        
        print(f"\n🧪 Predicting reaction for: {reactants_smiles}")
        
        # Optional wait
        if wait_time > 0:
            print(f"⏳ Waiting {wait_time} seconds before request...")
            time.sleep(wait_time)
        
        try:
            # Record request time
            self.last_request_time = time.time()
            
            # Make prediction request
            print("📤 Submitting prediction request...")
            response = self.rxn.predict_reaction(reactants_smiles)
            
            # Check response
            if 'response' in response and 'status' in response['response']:
                status = response['response']['status']
                
                if status == 429:
                    print("\n⚠️  RATE LIMITED!")
                    print("   IBM RXN has very strict rate limits.")
                    print("   Please wait at least 5-10 minutes before trying again.")
                    print("   You can use the manual retry option below.")
                    return None
                elif status == 401:
                    print("✗ Authentication failed - check API key")
                    return None
                elif status == 403:
                    print("✗ Forbidden - check project ID or permissions")
                    return None
                elif status != 200:
                    print(f"✗ API Error {status}: {response['response'].get('message', 'Unknown')}")
                    return None
            
            # Look for prediction ID
            prediction_id = self._extract_prediction_id(response)
            if not prediction_id:
                print("✗ No prediction ID found in response")
                print("Response:", json.dumps(response, indent=2))
                return None
            
            print(f"📋 Prediction ID: {prediction_id}")
            
            # Poll for results
            return self._poll_for_results(prediction_id)
            
        except Exception as e:
            print(f"✗ Request failed: {e}")
            return None
    
    def _extract_prediction_id(self, response):
        """Extract prediction ID from response."""
        if 'prediction_id' in response:
            return response['prediction_id']
        elif 'response' in response and 'payload' in response['response']:
            payload = response['response']['payload']
            if 'id' in payload:
                return payload['id']
            elif 'prediction_id' in payload:
                return payload['prediction_id']
        return None
    
    def _poll_for_results(self, prediction_id):
        """Poll for prediction results."""
        print("⏳ Polling for results...")
        
        for attempt in range(30):  # Poll for up to 5 minutes
            time.sleep(10)  # Wait 10 seconds between polls
            
            try:
                result = self.rxn.get_predict_reaction_results(prediction_id)
                
                # Check status
                status = None
                if 'response' in result and 'payload' in result['response']:
                    status = result['response']['payload'].get('status', 'UNKNOWN')
                elif 'status' in result:
                    status = result['status']
                
                print(f"   Poll {attempt + 1}: Status = {status}")
                
                if status == 'SUCCESS':
                    print("✓ Prediction completed successfully!")
                    return result
                elif status == 'FAILURE':
                    print("✗ Prediction failed")
                    return None
                elif status == 'RUNNING' or status == 'NEW':
                    continue  # Keep polling
                else:
                    print(f"   Unknown status: {status}")
                    
            except Exception as e:
                print(f"   Error polling: {e}")
        
        print("⚠️  Polling timed out after 5 minutes")
        return None
    
    def display_results(self, result):
        """Display prediction results."""
        if not result:
            print("No results to display")
            return
        
        try:
            # Find attempts/predictions in result
            attempts = None
            if 'response' in result and 'payload' in result['response']:
                payload = result['response']['payload']
                if 'attempts' in payload:
                    attempts = payload['attempts']
                elif 'sequences' in payload:
                    attempts = payload['sequences']
            elif 'attempts' in result:
                attempts = result['attempts']
            
            if not attempts:
                print("✗ No prediction results found")
                print("Result structure:", json.dumps(result, indent=2))
                return
            
            print("\n" + "="*60)
            print("PREDICTION RESULTS")
            print("="*60)
            
            for i, attempt in enumerate(attempts, 1):
                smiles = attempt.get('smiles', 'N/A')
                confidence = attempt.get('confidence', 'N/A')
                
                print(f"\nPrediction {i}:")
                print(f"  Reaction SMILES: {smiles}")
                print(f"  Confidence: {confidence}")
                
                if '>>' in smiles:
                    reactants, products = smiles.split('>>')
                    print(f"  Reactants: {reactants}")
                    print(f"  Products: {products}")
            
        except Exception as e:
            print(f"✗ Error displaying results: {e}")
            print("Raw result:", json.dumps(result, indent=2))

def main():
    """Main function."""
    print("IBM RXN Product Prediction Tool")
    print("=" * 50)
    print("⚠️  Note: IBM RXN has very strict rate limits!")
    print("   If you get rate limited, wait 5-10 minutes before retrying.")
    
    # Initialize predictor
    predictor = IBMRXNPredictor()
    if not predictor.initialize():
        return
    
    # Interactive mode
    print("\nEnter SMILES for reactants (separated by '.')")
    print("Commands:")
    print("  'quit' or 'exit' - Exit the program")
    print("  'wait X' - Wait X seconds before next request")
    print("  'example' - Try a simple example reaction")
    
    while True:
        try:
            user_input = input("\n> ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if user_input.lower() == 'example':
                user_input = "CCO.CC(=O)O"
                print(f"Using example: {user_input}")
            
            if user_input.startswith('wait '):
                try:
                    wait_time = int(user_input.split()[1])
                    print(f"Will wait {wait_time} seconds before next request")
                    continue
                except:
                    print("Invalid wait command. Use: wait <seconds>")
                    continue
            
            if not user_input:
                print("Please enter valid SMILES or a command")
                continue
            
            # Check if enough time has passed since last request
            time_since_last = time.time() - predictor.last_request_time
            if time_since_last < 300:  # 5 minutes
                remaining = 300 - time_since_last
                print(f"⚠️  Recommended to wait {remaining:.0f} more seconds to avoid rate limiting")
                confirm = input("Continue anyway? (y/N): ").strip().lower()
                if confirm != 'y':
                    continue
            
            # Make prediction
            result = predictor.predict_reaction(user_input)
            predictor.display_results(result)
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
