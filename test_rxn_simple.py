from rxn4chemistry import RXN4ChemistryWrapper
import time

# API credentials
API_KEY = "apk-56d66d912338dc5344a7fbdc39339d5c119d5d655529f5964978d5b90609d1c5"
PROJECT_ID = "6870ee4095b316f61f21aac6"

print("Initializing RXN4Chemistry wrapper...")
rxn = RXN4ChemistryWrapper(api_key=API_KEY, project_id=PROJECT_ID)
print("✅ Wrapper initialized")

# Test 1: Check projects
print("\nTest 1: Listing projects...")
try:
    projects = rxn.list_all_projects()
    print(f"✅ Found {len(projects)} projects")
    for project in projects:
        print(f"  - Project: {project.get('name', 'Unknown')} (ID: {project.get('id', 'Unknown')})")
except Exception as e:
    print(f"❌ Error listing projects: {e}")

# Test 2: Try a very simple prediction with long wait
print("\nTest 2: Waiting 2 minutes before attempting prediction...")
time.sleep(120)

print("Attempting simple reaction prediction...")
try:
    # Use the exact example from documentation
    reaction_input = "BrBr.c1ccc2cc3ccccc3cc2c1"
    response = rxn.predict_reaction(reaction_input)
    print("✅ Prediction request submitted!")
    print("Response:", response)
    
    if 'prediction_id' in response:
        prediction_id = response['prediction_id']
        print(f"Prediction ID: {prediction_id}")
        
        # Poll for results
        print("Polling for results...")
        for i in range(10):  # Try 10 times
            time.sleep(30)  # Wait 30 seconds between polls
            try:
                result = rxn.get_predict_reaction_results(prediction_id)
                print(f"Poll {i+1}: {result}")
                
                if ('response' in result and 'payload' in result['response'] and 
                    'status' in result['response']['payload']):
                    status = result['response']['payload']['status']
                    if status == 'SUCCESS':
                        print("✅ Prediction successful!")
                        if 'attempts' in result['response']['payload']:
                            smiles = result['response']['payload']['attempts'][0]['smiles']
                            print(f"Predicted reaction: {smiles}")
                        break
                    elif status == 'FAILED':
                        print("❌ Prediction failed")
                        break
                    else:
                        print(f"Status: {status}, continuing to poll...")
            except Exception as e:
                print(f"Error polling: {e}")
                time.sleep(30)
    else:
        print("❌ No prediction_id in response")
        
except Exception as e:
    print(f"❌ Error with prediction: {e}")

print("\nTest completed.")
