"""
Reaction Prediction with IBM RXN and OpenAI fallback
Based on user preferences: IBM RXN first, then OpenAI as fallback
"""

from rxn4chemistry import RXN4ChemistryWrapper
import openai
import time
import json

# API credentials
IBM_RXN_API_KEY = "apk-56d66d912338dc5344a7fbdc39339d5c119d5d655529f5964978d5b90609d1c5"
IBM_RXN_PROJECT_ID = "6870ee4095b316f61f21aac6"

# OpenAI API key (from your memories)
OPENAI_API_KEY = "your_openai_api_key_here"  # You'll need to provide this

def try_ibm_rxn_prediction(reaction_input, max_wait_minutes=5):
    """
    Try to predict reaction using IBM RXN with conservative rate limiting
    """
    print("🧪 Attempting prediction with IBM RXN...")
    
    try:
        # Initialize wrapper
        rxn = RXN4ChemistryWrapper(api_key=IBM_RXN_API_KEY, project_id=IBM_RXN_PROJECT_ID)
        
        # Wait to avoid rate limiting
        print(f"Waiting {max_wait_minutes} minutes to avoid rate limiting...")
        time.sleep(max_wait_minutes * 60)
        
        # Submit prediction
        print("Submitting prediction...")
        response = rxn.predict_reaction(reaction_input)
        
        if 'prediction_id' in response:
            prediction_id = response['prediction_id']
            print(f"✅ Prediction submitted! ID: {prediction_id}")
            
            # Poll for results
            print("Polling for results...")
            for i in range(20):  # Try for up to 20 minutes
                time.sleep(60)  # Wait 1 minute between polls
                try:
                    result = rxn.get_predict_reaction_results(prediction_id)
                    
                    if ('response' in result and 'payload' in result['response'] and 
                        'status' in result['response']['payload']):
                        status = result['response']['payload']['status']
                        
                        if status == 'SUCCESS':
                            print("✅ IBM RXN prediction successful!")
                            if 'attempts' in result['response']['payload']:
                                smiles = result['response']['payload']['attempts'][0]['smiles']
                                return {'success': True, 'method': 'IBM RXN', 'result': smiles}
                            break
                        elif status == 'FAILED':
                            print("❌ IBM RXN prediction failed")
                            return {'success': False, 'method': 'IBM RXN', 'error': 'Prediction failed'}
                        else:
                            print(f"Status: {status}, continuing to poll...")
                            
                except Exception as e:
                    print(f"Error polling: {e}")
                    
            return {'success': False, 'method': 'IBM RXN', 'error': 'Timeout waiting for results'}
            
        else:
            # Check if it's a rate limiting issue
            if 'response' in response and response['response'].get('status') == 429:
                print("❌ Rate limited by IBM RXN API")
                return {'success': False, 'method': 'IBM RXN', 'error': 'Rate limited'}
            else:
                print(f"❌ Unexpected response: {response}")
                return {'success': False, 'method': 'IBM RXN', 'error': 'Unexpected response format'}
                
    except Exception as e:
        print(f"❌ IBM RXN error: {e}")
        return {'success': False, 'method': 'IBM RXN', 'error': str(e)}

def try_openai_prediction(reaction_input):
    """
    Fallback to OpenAI for reaction prediction
    """
    print("🤖 Falling back to OpenAI for reaction prediction...")
    
    try:
        openai.api_key = OPENAI_API_KEY
        
        prompt = f"""
        You are a chemistry expert. Predict the most likely product(s) of this chemical reaction.
        
        Reactants (SMILES): {reaction_input}
        
        Please provide:
        1. The most likely product in SMILES format
        2. A brief explanation of the reaction mechanism
        3. Confidence level (high/medium/low)
        
        Format your response as:
        Product SMILES: [SMILES]
        Mechanism: [explanation]
        Confidence: [level]
        """
        
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=500,
            temperature=0.1
        )
        
        result = response.choices[0].message.content
        print("✅ OpenAI prediction completed!")
        return {'success': True, 'method': 'OpenAI', 'result': result}
        
    except Exception as e:
        print(f"❌ OpenAI error: {e}")
        return {'success': False, 'method': 'OpenAI', 'error': str(e)}

def predict_reaction(reaction_input):
    """
    Main function to predict reaction using IBM RXN first, then OpenAI fallback
    """
    print(f"🔬 Predicting reaction for: {reaction_input}")
    print("=" * 50)
    
    # Try IBM RXN first (as per user preference)
    result = try_ibm_rxn_prediction(reaction_input, max_wait_minutes=1)  # Start with 1 minute wait
    
    if result['success']:
        print(f"\n✅ Success with {result['method']}!")
        print(f"Result: {result['result']}")
        return result
    else:
        print(f"\n❌ {result['method']} failed: {result['error']}")
        
        # Fallback to OpenAI
        print("\n🔄 Trying fallback method...")
        fallback_result = try_openai_prediction(reaction_input)
        
        if fallback_result['success']:
            print(f"\n✅ Success with {fallback_result['method']}!")
            print(f"Result: {fallback_result['result']}")
            return fallback_result
        else:
            print(f"\n❌ All methods failed!")
            return {'success': False, 'error': 'All prediction methods failed'}

if __name__ == "__main__":
    # Test with the original reaction
    reaction_input = "CC(C)CO.O=C=O"  # isobutanol + CO2
    
    print("🧪 Reaction Prediction System")
    print("Using IBM RXN first, OpenAI as fallback")
    print("=" * 50)
    
    result = predict_reaction(reaction_input)
    
    print("\n" + "=" * 50)
    print("FINAL RESULT:")
    print(json.dumps(result, indent=2))
